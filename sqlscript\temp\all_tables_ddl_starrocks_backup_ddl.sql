-- 备份表DDL语句
-- 生成时间: 2025-06-25

CREATE TABLE `AccountsPayCloseCycle_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `SettlementCycle`       decimal(12, 2) NULL COMMENT "",
    `Calculateyear`         varchar(65533) NULL COMMENT "",
    `Last_change_date`      date           NULL COMMENT "",
    `SettlementDays`        decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Module`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `AccountsPayableRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `AccountsReceivableRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `AccountsReceiveRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `AdvancePaymentPeriod_20250625_bak`
(
    `eid`                    varchar(65533) NOT NULL COMMENT "",
    `Product_Line`           varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`        varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`         int(11)        NOT NULL COMMENT "",
    `Account_Set`            varchar(65533) NOT NULL COMMENT "",
    `Year`                   varchar(65533) NOT NULL COMMENT "",
    `Month`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`               varchar(65533) NULL COMMENT "",
    `collectedTime`          varchar(65533) NULL COMMENT "",
    `collectConfigId`        varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`    varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`  varchar(65533) NULL COMMENT "",
    `aiId`                   varchar(65533) NULL COMMENT "",
    `aiopsItem`              varchar(65533) NULL COMMENT "",
    `flumeTimestamp`         varchar(65533) NULL COMMENT "",
    `source_db_id`           varchar(65533) NULL COMMENT "",
    `Account_set_name`       varchar(65533) NULL COMMENT "",
    `advance_payment_period` decimal(12, 2) NULL COMMENT "",
    `Prepaid_time`           decimal(12, 2) NULL COMMENT "",
    `Prepaid_num`            int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BOMAuditcycle_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Audit_cycle`           decimal(12, 2) NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT "",
    `Total_audit_time`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BOMRateOfaudit_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `RateOfAudit`              decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `Total_number`             int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BankAccounts_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Account_type`          varchar(65533) NOT NULL COMMENT "",
    `currency`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `accounts_num`          int(11)        NULL COMMENT "",
    `StartDate`             date           NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `ExprityDate`, `Account_type`, `currency`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BankExpenseRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Module`, `Doc_code`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BankIncomeRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Module`, `Doc_code`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BankReceiptsNum_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `bank_receipts_num`     int(11)        NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `BillsReceiveNum_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `bills_receive_num`     int(11)        NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `CollectVerificationCycle_20250625_bak`
(
    `eid`                        varchar(65533) NOT NULL COMMENT "",
    `Product_Line`               varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`            varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`             int(11)        NOT NULL COMMENT "",
    `Account_Set`                varchar(65533) NOT NULL COMMENT "",
    `Year`                       varchar(65533) NOT NULL COMMENT "",
    `Month`                      varchar(65533) NOT NULL COMMENT "",
    `Type`                       varchar(65533) NOT NULL COMMENT "",
    `deviceId`                   varchar(65533) NULL COMMENT "",
    `collectedTime`              varchar(65533) NULL COMMENT "",
    `collectConfigId`            varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`        varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`      varchar(65533) NULL COMMENT "",
    `aiId`                       varchar(65533) NULL COMMENT "",
    `aiopsItem`                  varchar(65533) NULL COMMENT "",
    `flumeTimestamp`             varchar(65533) NULL COMMENT "",
    `source_db_id`               varchar(65533) NULL COMMENT "",
    `Account_set_name`           varchar(65533) NULL COMMENT "",
    `Collect_verification_cycle` decimal(12, 2) NULL COMMENT "",
    `Write_off_time`             decimal(12, 2) NULL COMMENT "",
    `Write_off_num`              int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `CollectVerificationRate_20250625_bak`
(
    `eid`                       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`              varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`            int(11)        NOT NULL COMMENT "",
    `Account_Set`               varchar(65533) NOT NULL COMMENT "",
    `Year`                      varchar(65533) NOT NULL COMMENT "",
    `Month`                     varchar(65533) NOT NULL COMMENT "",
    `deviceId`                  varchar(65533) NULL COMMENT "",
    `collectedTime`             varchar(65533) NULL COMMENT "",
    `collectConfigId`           varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`       varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`     varchar(65533) NULL COMMENT "",
    `aiId`                      varchar(65533) NULL COMMENT "",
    `aiopsItem`                 varchar(65533) NULL COMMENT "",
    `flumeTimestamp`            varchar(65533) NULL COMMENT "",
    `source_db_id`              varchar(65533) NULL COMMENT "",
    `Account_set_name`          varchar(65533) NULL COMMENT "",
    `Collect_verification_rate` decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count`  int(11)        NULL COMMENT "",
    `Total_number`              int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `CompleteOnTimeRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `CompleteOnTimeRate`    decimal(12, 2) NULL COMMENT "",
    `OnTimeNum`             int(11)        NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `CompleteRateOfWork_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Work_Type`             varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `title_name`            varchar(65533) NULL COMMENT "",
    `CompleteOnTimeRate`    decimal(12, 2) NULL COMMENT "",
    `OnTimeNum`             int(11)        NULL COMMENT "",
    `delay_num`             int(11)        NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Work_Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `CostCompositionRatio_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Material_proportion`   decimal(12, 2) NULL COMMENT "",
    `Labor_proportion`      decimal(12, 2) NULL COMMENT "",
    `Processing_fees`       decimal(12, 2) NULL COMMENT "",
    `Production_costs1`     decimal(12, 2) NULL COMMENT "",
    `Production_costs2`     decimal(12, 2) NULL COMMENT "",
    `Production_costs3`     decimal(12, 2) NULL COMMENT "",
    `Production_costs4`     decimal(12, 2) NULL COMMENT "",
    `Production_costs5`     decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DaysPayableOutstanding_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Accounts_payable_rate` decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DeliveryRateForProcurement_20250625_bak`
(
    `eid`                       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`              varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`            int(11)        NOT NULL COMMENT "",
    `Account_Set`               varchar(65533) NOT NULL COMMENT "",
    `Year`                      varchar(65533) NOT NULL COMMENT "",
    `Month`                     varchar(65533) NOT NULL COMMENT "",
    `deviceId`                  varchar(65533) NULL COMMENT "",
    `collectedTime`             varchar(65533) NULL COMMENT "",
    `collectConfigId`           varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`       varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`     varchar(65533) NULL COMMENT "",
    `aiId`                      varchar(65533) NULL COMMENT "",
    `aiopsItem`                 varchar(65533) NULL COMMENT "",
    `flumeTimestamp`            varchar(65533) NULL COMMENT "",
    `source_db_id`              varchar(65533) NULL COMMENT "",
    `Account_set_name`          varchar(65533) NULL COMMENT "",
    `Procurement_delivery_rate` decimal(12, 2) NULL COMMENT "",
    `Zhunjiao_Bishu`            int(11)        NULL COMMENT "",
    `Total_number_of_purchases` int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DeliveryRateOfOrders_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         datetime       NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Order_delivery_rate`   decimal(12, 2) NULL COMMENT "",
    `Zhunjiao_Bishu`        int(11)        NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Year`, `Month`, `Account_Set`, `enterpriseCode`, `IndicatorNumber`, `Product_Line`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DeviationRateBetweenOrderPrice_20250625_bak`
(
    `eid`                            varchar(65533) NOT NULL COMMENT "",
    `Product_Line`                   varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`                varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`                 int(11)        NOT NULL COMMENT "",
    `Account_Set`                    varchar(65533) NOT NULL COMMENT "",
    `Year`                           varchar(65533) NOT NULL COMMENT "",
    `Month`                          varchar(65533) NOT NULL COMMENT "",
    `deviceId`                       varchar(65533) NULL COMMENT "",
    `collectedTime`                  varchar(65533) NULL COMMENT "",
    `collectConfigId`                varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`            varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`          varchar(65533) NULL COMMENT "",
    `aiId`                           varchar(65533) NULL COMMENT "",
    `aiopsItem`                      varchar(65533) NULL COMMENT "",
    `flumeTimestamp`                 varchar(65533) NULL COMMENT "",
    `source_db_id`                   varchar(65533) NULL COMMENT "",
    `Account_set_name`               varchar(65533) NULL COMMENT "",
    `Deviation_rate`                 decimal(12, 2) NULL COMMENT "",
    `Difference_between_order_price` decimal(12, 2) NULL COMMENT "",
    `Evaluated_unit_price`           decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DeviationRatePurchasePrice_20250625_bak`
(
    `eid`                           varchar(65533) NOT NULL COMMENT "",
    `Product_Line`                  varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`               varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`                int(11)        NOT NULL COMMENT "",
    `Account_Set`                   varchar(65533) NOT NULL COMMENT "",
    `Year`                          varchar(65533) NOT NULL COMMENT "",
    `Month`                         varchar(65533) NOT NULL COMMENT "",
    `deviceId`                      varchar(65533) NULL COMMENT "",
    `collectedTime`                 varchar(65533) NULL COMMENT "",
    `collectConfigId`               varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`           varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`         varchar(65533) NULL COMMENT "",
    `aiId`                          varchar(65533) NULL COMMENT "",
    `aiopsItem`                     varchar(65533) NULL COMMENT "",
    `flumeTimestamp`                varchar(65533) NULL COMMENT "",
    `source_db_id`                  varchar(65533) NULL COMMENT "",
    `Account_set_name`              varchar(65533) NULL COMMENT "",
    `Deviation_Rate_Purchase_Price` decimal(12, 2) NULL COMMENT "",
    `Differences_in_procurement`    decimal(12, 2) NULL COMMENT "",
    `unit_price`                    decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DocumentDeductionOnTime_20250625_bak`
(
    `eid`                     varchar(65533) NOT NULL COMMENT "",
    `Product_Line`            varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`         varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`          int(11)        NOT NULL COMMENT "",
    `Account_Set`             varchar(65533) NOT NULL COMMENT "",
    `StartDate`               date           NOT NULL COMMENT "",
    `ExprityDate`             date           NOT NULL COMMENT "",
    `Module`                  varchar(65533) NOT NULL COMMENT "",
    `Doc_code`                varchar(65533) NOT NULL COMMENT "",
    `deviceId`                varchar(65533) NULL COMMENT "",
    `collectedTime`           varchar(65533) NULL COMMENT "",
    `collectConfigId`         varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`     varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`   varchar(65533) NULL COMMENT "",
    `aiId`                    varchar(65533) NULL COMMENT "",
    `aiopsItem`               varchar(65533) NULL COMMENT "",
    `flumeTimestamp`          varchar(65533) NULL COMMENT "",
    `source_db_id`            varchar(65533) NULL COMMENT "",
    `Account_set_name`        varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`     int(11)        NULL COMMENT "",
    `Num_timely_deductions`   int(11)        NULL COMMENT "",
    `Num_timely_nodeductions` int(11)        NULL COMMENT "",
    `Proportion_of_timely`    decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DocumentDeductionTimely_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Timely_deduction_rate` decimal(12, 2) NULL COMMENT "",
    `Num_timely_deductions` int(11)        NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DocumentQuantity_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `DocumentReplenishmentRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ECNAuditcycle_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Audit_cycle`           decimal(12, 2) NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT "",
    `Total_audit_time`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ECNRateOfaudit_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `RateOfAudit`              decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `Total_number`             int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ECNReplaceNumber_20250625_bak`
(
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `ECNchange_num`         int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Month`, `Year`, `eid`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `Financialreplenishmentrate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Module`, `Doc_code`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `IncreaseInMaterials_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Items_add_Num`         int(11)        NULL COMMENT "",
    `BOM_add_Num`           int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `InventoryTurnover_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `InventoryTurnoverRate` decimal(12, 2) NULL COMMENT "",
    `CostofGoods`           decimal(12, 2) NULL COMMENT "",
    `BeginningInventory`    decimal(12, 2) NULL COMMENT "",
    `EndingInventory`       decimal(12, 2) NULL COMMENT "",
    `AverageInventory`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `InventoryTurnoverProduct_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `ProductCategories`     varchar(65533) NULL COMMENT "",
    `InventoryTurnoverRate` decimal(12, 2) NULL COMMENT "",
    `CostofGoods`           decimal(12, 2) NULL COMMENT "",
    `BeginningInventory`    decimal(12, 2) NULL COMMENT "",
    `EndingInventory`       decimal(12, 2) NULL COMMENT "",
    `AverageInventory`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Month`, `Year`, `Account_Set`, `enterpriseCode`, `IndicatorNumber`, `Product_Line`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `InvoicingVolume_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `InvoicingVolume`       int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `LeadTimesRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `LeadTimesRate`         decimal(12, 2) NULL COMMENT "",
    `NumberOfPensSet`       int(11)        NULL COMMENT "",
    `NumOfNeedtoSet`        int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MISCAccountSubjects_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `LedgerAccount`         varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `AccountSubjectName`    varchar(65533) NULL COMMENT "",
    `TransactionNum`        int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `LedgerAccount`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MISCReasonCode_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `ReasonCode`            varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `ReasonCodeName`        varchar(65533) NULL COMMENT "",
    `TransactionNum`        int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `ReasonCode`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MaterialInventoryTurnoverDay_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `ProductCategories`     varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `InventoryTurnoverRate` decimal(12, 2) NULL COMMENT "",
    `CostofGoods`           decimal(12, 2) NULL COMMENT "",
    `BeginningInventory`    decimal(12, 2) NULL COMMENT "",
    `EndingInventory`       decimal(12, 2) NULL COMMENT "",
    `AverageInventory`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `ProductCategories`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MaterialIssuanceCycle_20250625_bak`
(
    `eid`                     varchar(65533) NOT NULL COMMENT "",
    `Product_Line`            varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`         varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`          int(11)        NOT NULL COMMENT "",
    `Account_Set`             varchar(65533) NOT NULL COMMENT "",
    `Year`                    varchar(65533) NOT NULL COMMENT "",
    `Month`                   varchar(65533) NOT NULL COMMENT "",
    `deviceId`                varchar(65533) NULL COMMENT "",
    `collectedTime`           varchar(65533) NULL COMMENT "",
    `collectConfigId`         varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`     varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`   varchar(65533) NULL COMMENT "",
    `aiId`                    varchar(65533) NULL COMMENT "",
    `aiopsItem`               varchar(65533) NULL COMMENT "",
    `flumeTimestamp`          varchar(65533) NULL COMMENT "",
    `source_db_id`            varchar(65533) NULL COMMENT "",
    `Account_set_name`        varchar(65533) NULL COMMENT "",
    `Material_Issuance_Cycle` decimal(12, 2) NULL COMMENT "",
    `Total_number_work`       int(11)        NULL COMMENT "",
    `Issuance_total_time`     decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MissingrateOfWorkHour_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Missingrate_WorkHour`  decimal(12, 2) NULL COMMENT "",
    `Missingnum_WorkHour`   int(11)        NULL COMMENT "",
    `Total_number_items`    int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MissingworkRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Missing_work_rate`     decimal(12, 2) NULL COMMENT "",
    `Unreported_number`     int(11)        NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ModuleMonthlyDays_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `SettlementCycle`       decimal(12, 2) NULL COMMENT "",
    `Calculateyear`         varchar(65533) NULL COMMENT "",
    `Last_change_date`      date           NULL COMMENT "",
    `SettlementDays`        decimal(12, 2) NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Module`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `MonthlyIncreaseInPurchase_20250625_bak`
(
    `eid`                           varchar(65533) NOT NULL COMMENT "",
    `Product_Line`                  varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`               varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`                int(11)        NOT NULL COMMENT "",
    `Account_Set`                   varchar(65533) NOT NULL COMMENT "",
    `Year`                          varchar(65533) NOT NULL COMMENT "",
    `Month`                         varchar(65533) NOT NULL COMMENT "",
    `deviceId`                      varchar(65533) NULL COMMENT "",
    `collectedTime`                 varchar(65533) NULL COMMENT "",
    `collectConfigId`               varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`           varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`         varchar(65533) NULL COMMENT "",
    `aiId`                          varchar(65533) NULL COMMENT "",
    `aiopsItem`                     varchar(65533) NULL COMMENT "",
    `flumeTimestamp`                varchar(65533) NULL COMMENT "",
    `source_db_id`                  varchar(65533) NULL COMMENT "",
    `Account_set_name`              varchar(65533) NULL COMMENT "",
    `MonthlyincreaseInpurchase`     int(11)        NULL COMMENT "",
    `MonthlyincreaseInreceipt`      int(11)        NULL COMMENT "",
    `Monthly_increase_In_inventory` int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `NumOfBankPay_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Num_BankPay`           int(11)        NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `NumberOfBillsPay_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Number_BillsPay`       int(11)        NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `NumberOfOrderChanges_20250625_bak`
(
    `eid`                        varchar(65533) NOT NULL COMMENT "",
    `Product_Line`               varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`            varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`             int(11)        NOT NULL COMMENT "",
    `Account_Set`                varchar(65533) NOT NULL COMMENT "",
    `Year`                       varchar(65533) NOT NULL COMMENT "",
    `Month`                      varchar(65533) NOT NULL COMMENT "",
    `deviceId`                   varchar(65533) NULL COMMENT "",
    `collectedTime`              varchar(65533) NULL COMMENT "",
    `collectConfigId`            varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`        varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`      varchar(65533) NULL COMMENT "",
    `aiId`                       varchar(65533) NULL COMMENT "",
    `aiopsItem`                  varchar(65533) NULL COMMENT "",
    `flumeTimestamp`             varchar(65533) NULL COMMENT "",
    `source_db_id`               varchar(65533) NULL COMMENT "",
    `Account_set_name`           varchar(65533) NULL COMMENT "",
    `order_change_num`           decimal(12, 2) NULL COMMENT "",
    `Total_number_change_orders` decimal(12, 2) NULL COMMENT "",
    `Total_number_change`        decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OrderChangeCoverageRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_orders`   int(11)        NULL COMMENT "",
    `changes_covered_num`   int(11)        NULL COMMENT "",
    `Change_num`            int(11)        NULL COMMENT "",
    `Change_frequency`      decimal(12, 2) NULL COMMENT "",
    `CoverageRate`          decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OrderIncrease_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Increase_orders`       int(11)        NULL COMMENT "",
    `Increase_shipment`     int(11)        NULL COMMENT "",
    `No_order_shipment`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OrderLeadTime_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Order_lead_time`       decimal(12, 2) NULL COMMENT "",
    `Total_number_orders`   int(11)        NULL COMMENT "",
    `Delivery_time`         decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OrdertoWork_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `OrdertoWork`           decimal(12, 2) NULL COMMENT "",
    `Total_number_orders`   int(11)        NULL COMMENT "",
    `OrdertoWorkTime`       decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OverdueAccountsPayable_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `overdue_accounts_pay`  decimal(12, 2) NULL COMMENT "",
    `overdue_pay_num`       int(11)        NULL COMMENT "",
    `accounts_pay_num`      int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `OverdueAccountsReceiveRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `overdue_receive_rate`  decimal(12, 2) NULL COMMENT "",
    `overdue_receive_num`   int(11)        NULL COMMENT "",
    `accounts_receive_num`  int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `PayableReconciliationRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Module`                varchar(65533) NOT NULL COMMENT "",
    `Doc_code`              varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Total_number_of_doc`   int(11)        NULL COMMENT "",
    `Supplementary_orders`  int(11)        NULL COMMENT "",
    `make_up_order_rate`    decimal(12, 2) NULL COMMENT "",
    `Replenishment_time`    decimal(12, 2) NULL COMMENT "",
    `Document_review_time`  decimal(12, 2) NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Module`, `Doc_code`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `PaymentApprovalCycle_20250625_bak`
(
    `eid`                    varchar(65533) NOT NULL COMMENT "",
    `Product_Line`           varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`        varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`         int(11)        NOT NULL COMMENT "",
    `Account_Set`            varchar(65533) NOT NULL COMMENT "",
    `Year`                   varchar(65533) NOT NULL COMMENT "",
    `Month`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`               varchar(65533) NULL COMMENT "",
    `collectedTime`          varchar(65533) NULL COMMENT "",
    `collectConfigId`        varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`    varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`  varchar(65533) NULL COMMENT "",
    `aiId`                   varchar(65533) NULL COMMENT "",
    `aiopsItem`              varchar(65533) NULL COMMENT "",
    `flumeTimestamp`         varchar(65533) NULL COMMENT "",
    `source_db_id`           varchar(65533) NULL COMMENT "",
    `Account_set_name`       varchar(65533) NULL COMMENT "",
    `Payment_approval_cycle` decimal(12, 2) NULL COMMENT "",
    `Payment_approval_time`  decimal(12, 2) NULL COMMENT "",
    `Payment_approval_num`   int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `PaymentVerificationCycle_20250625_bak`
(
    `eid`                        varchar(65533) NOT NULL COMMENT "",
    `Product_Line`               varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`            varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`             int(11)        NOT NULL COMMENT "",
    `Account_Set`                varchar(65533) NOT NULL COMMENT "",
    `Year`                       varchar(65533) NOT NULL COMMENT "",
    `Month`                      varchar(65533) NOT NULL COMMENT "",
    `Type`                       varchar(65533) NOT NULL COMMENT "",
    `deviceId`                   varchar(65533) NULL COMMENT "",
    `collectedTime`              varchar(65533) NULL COMMENT "",
    `collectConfigId`            varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`        varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`      varchar(65533) NULL COMMENT "",
    `aiId`                       varchar(65533) NULL COMMENT "",
    `aiopsItem`                  varchar(65533) NULL COMMENT "",
    `flumeTimestamp`             varchar(65533) NULL COMMENT "",
    `source_db_id`               varchar(65533) NULL COMMENT "",
    `Account_set_name`           varchar(65533) NULL COMMENT "",
    `Payment_verification_cycle` decimal(12, 2) NULL COMMENT "",
    `payment_verification_time`  decimal(12, 2) NULL COMMENT "",
    `payment_verification_num`   int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProcurementCycle_20250625_bak`
(
    `eid`                       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`              varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`            int(11)        NOT NULL COMMENT "",
    `Account_Set`               varchar(65533) NOT NULL COMMENT "",
    `Year`                      varchar(65533) NOT NULL COMMENT "",
    `Month`                     varchar(65533) NOT NULL COMMENT "",
    `deviceId`                  varchar(65533) NULL COMMENT "",
    `collectedTime`             varchar(65533) NULL COMMENT "",
    `collectConfigId`           varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`       varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`     varchar(65533) NULL COMMENT "",
    `aiId`                      varchar(65533) NULL COMMENT "",
    `aiopsItem`                 varchar(65533) NULL COMMENT "",
    `flumeTimestamp`            varchar(65533) NULL COMMENT "",
    `source_db_id`              varchar(65533) NULL COMMENT "",
    `Account_set_name`          varchar(65533) NULL COMMENT "",
    `Procurement_cycle`         decimal(12, 2) NULL COMMENT "",
    `Total_number_of_purchases` int(11)        NULL COMMENT "",
    `Total_procurement_time`    decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProcurementSourcesRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `CoverageRate`          decimal(12, 2) NULL COMMENT "",
    `TransferRequestsNum`   int(11)        NULL COMMENT "",
    `PurchaseRequestsNum`   int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProcurementTypeDeliveryRate_20250625_bak`
(
    `eid`                       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`              varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`           varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`            int(11)        NOT NULL COMMENT "",
    `Account_Set`               varchar(65533) NOT NULL COMMENT "",
    `StartDate`                 date           NOT NULL COMMENT "",
    `ExprityDate`               date           NOT NULL COMMENT "",
    `Doc_category`              varchar(65533) NOT NULL COMMENT "",
    `deviceId`                  varchar(65533) NULL COMMENT "",
    `collectedTime`             varchar(65533) NULL COMMENT "",
    `collectConfigId`           varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`       varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`     varchar(65533) NULL COMMENT "",
    `aiId`                      varchar(65533) NULL COMMENT "",
    `aiopsItem`                 varchar(65533) NULL COMMENT "",
    `flumeTimestamp`            varchar(65533) NULL COMMENT "",
    `source_db_id`              varchar(65533) NULL COMMENT "",
    `Account_set_name`          varchar(65533) NULL COMMENT "",
    `Procurement_delivery_rate` decimal(12, 2) NULL COMMENT "",
    `Zhunjiao_Bishu`            int(11)        NULL COMMENT "",
    `Total_number`              int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Doc_category`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProductInventoryTurnover_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `ProductCategories`     varchar(65533) NULL COMMENT "",
    `InventoryTurnoverRate` decimal(12, 2) NULL COMMENT "",
    `CostofGoods`           decimal(12, 2) NULL COMMENT "",
    `BeginningInventory`    decimal(12, 2) NULL COMMENT "",
    `EndingInventory`       decimal(12, 2) NULL COMMENT "",
    `AverageInventory`      decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProductProductionCycle_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `Product_production_cycle` decimal(12, 2) NULL COMMENT "",
    `Total_number_work`        int(11)        NULL COMMENT "",
    `Total_production_time`    decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProductionCycle_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `production_cycle`      decimal(12, 2) NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT "",
    `Total_production_time` decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProductionSourceRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `CoverageRate`          decimal(12, 2) NULL COMMENT "",
    `Transferred_Work_Num`  int(11)        NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ProductionYield_20250625_bak`
(
    `eid`                    varchar(65533) NOT NULL COMMENT "",
    `Product_Line`           varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`        varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`         int(11)        NOT NULL COMMENT "",
    `Account_Set`            varchar(65533) NOT NULL COMMENT "",
    `Year`                   varchar(65533) NOT NULL COMMENT "",
    `Month`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`               varchar(65533) NULL COMMENT "",
    `collectedTime`          varchar(65533) NULL COMMENT "",
    `collectConfigId`        varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`    varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`  varchar(65533) NULL COMMENT "",
    `aiId`                   varchar(65533) NULL COMMENT "",
    `aiopsItem`              varchar(65533) NULL COMMENT "",
    `flumeTimestamp`         varchar(65533) NULL COMMENT "",
    `source_db_id`           varchar(65533) NULL COMMENT "",
    `Account_set_name`       varchar(65533) NULL COMMENT "",
    `production_yield`       decimal(12, 2) NULL COMMENT "",
    `production_quantity`    decimal(12, 2) NULL COMMENT "",
    `quantity_good_products` decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `PurchaseQualityDefectRate_20250625_bak`
(
    `eid`                          varchar(65533) NOT NULL COMMENT "",
    `Product_Line`                 varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`              varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`               int(11)        NOT NULL COMMENT "",
    `Account_Set`                  varchar(65533) NOT NULL COMMENT "",
    `Year`                         varchar(65533) NOT NULL COMMENT "",
    `Month`                        varchar(65533) NOT NULL COMMENT "",
    `deviceId`                     varchar(65533) NULL COMMENT "",
    `collectedTime`                varchar(65533) NULL COMMENT "",
    `collectConfigId`              varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`          varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`        varchar(65533) NULL COMMENT "",
    `aiId`                         varchar(65533) NULL COMMENT "",
    `aiopsItem`                    varchar(65533) NULL COMMENT "",
    `flumeTimestamp`               varchar(65533) NULL COMMENT "",
    `source_db_id`                 varchar(65533) NULL COMMENT "",
    `Account_set_name`             varchar(65533) NULL COMMENT "",
    `Purchase_quality_defect_rate` decimal(12, 2) NULL COMMENT "",
    `NumberofDefects`              decimal(12, 2) NULL COMMENT "",
    `Received_quantity`            decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `PurchaseZeroUnitPriceRatio_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `zero_unit_price_ratio` decimal(12, 2) NULL COMMENT "",
    `zero_unit_price_num`   int(11)        NULL COMMENT "",
    `Total_number`          int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `RateBetweenInvoiceAndPurchase_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `rate_purchase_invoice` decimal(12, 2) NULL COMMENT "",
    `diff_purchase_invoice` decimal(12, 2) NULL COMMENT "",
    `Purchase_price`        decimal(12, 2) NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `RateBetweenInvoiceOrder_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `rate_invoice_order`    decimal(12, 2) NULL COMMENT "",
    `diff_invoice_order`    decimal(12, 2) NULL COMMENT "",
    `order_price`           decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `RateOfOrderChange_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `Order_change_rate`        decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `Change_num`               int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `RateOfPreparationLeadTime_20250625_bak`
(
    `Year`                      varchar(65533) NOT NULL COMMENT "",
    `Month`                     varchar(65533) NOT NULL COMMENT "",
    `Account_Set`               varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`            int(11)        NOT NULL COMMENT "",
    `IndicatorNumber`           varchar(65533) NOT NULL COMMENT "",
    `eid`                       varchar(65533) NOT NULL COMMENT "",
    `Product_Line`              varchar(65533) NOT NULL COMMENT "",
    `source_db_id`              varchar(65533) NULL COMMENT "",
    `deviceId`                  varchar(65533) NULL COMMENT "",
    `collectedTime`             varchar(65533) NULL COMMENT "",
    `collectConfigId`           varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`       varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`     varchar(65533) NULL COMMENT "",
    `aiId`                      varchar(65533) NULL COMMENT "",
    `aiopsItem`                 varchar(65533) NULL COMMENT "",
    `flumeTimestamp`            varchar(65533) NULL COMMENT "",
    `Account_set_name`          varchar(65533) NULL COMMENT "",
    `RateOfMaterialPreparation` decimal(12, 2) NULL COMMENT "",
    `NumberOfPensSet`           int(11)        NULL COMMENT "",
    `NumOfNeedtoSet`            int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`Year`, `Month`, `Account_Set`, `enterpriseCode`, `IndicatorNumber`, `eid`, `Product_Line`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `ReceivableTurnoverRate_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `Receivable_turnover_rate` decimal(12, 2) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `SemiFinishProductProductionCycle_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `SemiFinishCycle`       decimal(12, 2) NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT "",
    `Total_production_time` decimal(12, 2) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `StartRateOfWork_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `StartDate`             date           NOT NULL COMMENT "",
    `ExprityDate`           date           NOT NULL COMMENT "",
    `Work_Type`             varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `title_name`            varchar(65533) NULL COMMENT "",
    `Start_work_rate`       decimal(12, 2) NULL COMMENT "",
    `OnTimeNum`             int(11)        NULL COMMENT "",
    `delay_num`             int(11)        NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `StartDate`, `ExprityDate`, `Work_Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `StartWorkRate_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `Start_work_rate`       decimal(12, 2) NULL COMMENT "",
    `OnTimeNum`             int(11)        NULL COMMENT "",
    `Total_number_work`     int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `TimelyCompletionOfWork_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `Timely_completion_work`   decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `Total_number_work`        int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `TransactionOfBillsPay_20250625_bak`
(
    `eid`                   varchar(65533) NOT NULL COMMENT "",
    `Product_Line`          varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`       varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`        int(11)        NOT NULL COMMENT "",
    `Account_Set`           varchar(65533) NOT NULL COMMENT "",
    `Year`                  varchar(65533) NOT NULL COMMENT "",
    `Month`                 varchar(65533) NOT NULL COMMENT "",
    `Type`                  varchar(65533) NOT NULL COMMENT "",
    `deviceId`              varchar(65533) NULL COMMENT "",
    `collectedTime`         varchar(65533) NULL COMMENT "",
    `collectConfigId`       varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`   varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId` varchar(65533) NULL COMMENT "",
    `aiId`                  varchar(65533) NULL COMMENT "",
    `aiopsItem`             varchar(65533) NULL COMMENT "",
    `flumeTimestamp`        varchar(65533) NULL COMMENT "",
    `source_db_id`          varchar(65533) NULL COMMENT "",
    `Account_set_name`      varchar(65533) NULL COMMENT "",
    `TransNumOfBillsPay`    int(11)        NULL COMMENT "",
    `IdentifyYear`          datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `TransactionsOfNotesReceive_20250625_bak`
(
    `eid`                          varchar(65533) NOT NULL COMMENT "",
    `Product_Line`                 varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`              varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`               int(11)        NOT NULL COMMENT "",
    `Account_Set`                  varchar(65533) NOT NULL COMMENT "",
    `Year`                         varchar(65533) NOT NULL COMMENT "",
    `Month`                        varchar(65533) NOT NULL COMMENT "",
    `Type`                         varchar(65533) NOT NULL COMMENT "",
    `deviceId`                     varchar(65533) NULL COMMENT "",
    `collectedTime`                varchar(65533) NULL COMMENT "",
    `collectConfigId`              varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`          varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`        varchar(65533) NULL COMMENT "",
    `aiId`                         varchar(65533) NULL COMMENT "",
    `aiopsItem`                    varchar(65533) NULL COMMENT "",
    `flumeTimestamp`               varchar(65533) NULL COMMENT "",
    `source_db_id`                 varchar(65533) NULL COMMENT "",
    `Account_set_name`             varchar(65533) NULL COMMENT "",
    `Transaction_of_notes_receive` int(11)        NULL COMMENT "",
    `IdentifyYear`                 datetime       NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`, `Type`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `WorkOrderReportRateBatch_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `reporting_rate`           decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `reporting_num`            int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

CREATE TABLE `WorkOrderReportRateSingle_20250625_bak`
(
    `eid`                      varchar(65533) NOT NULL COMMENT "",
    `Product_Line`             varchar(65533) NOT NULL COMMENT "",
    `IndicatorNumber`          varchar(65533) NOT NULL COMMENT "",
    `enterpriseCode`           int(11)        NOT NULL COMMENT "",
    `Account_Set`              varchar(65533) NOT NULL COMMENT "",
    `Year`                     varchar(65533) NOT NULL COMMENT "",
    `Month`                    varchar(65533) NOT NULL COMMENT "",
    `deviceId`                 varchar(65533) NULL COMMENT "",
    `collectedTime`            varchar(65533) NULL COMMENT "",
    `collectConfigId`          varchar(65533) NULL COMMENT "",
    `uploadDataModelCode`      varchar(65533) NULL COMMENT "",
    `deviceCollectDetailId`    varchar(65533) NULL COMMENT "",
    `aiId`                     varchar(65533) NULL COMMENT "",
    `aiopsItem`                varchar(65533) NULL COMMENT "",
    `flumeTimestamp`           varchar(65533) NULL COMMENT "",
    `source_db_id`             varchar(65533) NULL COMMENT "",
    `Account_set_name`         varchar(65533) NULL COMMENT "",
    `reporting_rate`           decimal(12, 2) NULL COMMENT "",
    `Timely_transaction_count` int(11)        NULL COMMENT "",
    `reporting_num`            int(11)        NULL COMMENT ""
) ENGINE = OLAP PRIMARY KEY(`eid`, `Product_Line`, `IndicatorNumber`, `enterpriseCode`, `Account_Set`, `Year`, `Month`)
COMMENT "OLAP"
DISTRIBUTED BY HASH(`eid`) BUCKETS 10
PROPERTIES (
"replication_num" = "2",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);

