# -*- coding: utf-8 -*-
"""
MySQL SQL脚本执行器 - Python 2.7.5专用版本
逐行执行SQL文件中的语句，每执行一个SQL后暂停1分钟
"""

import MySQLdb
import time
import sys
import os
import codecs
from datetime import datetime

class MySQLExecutor(object):
    def __init__(self, host, port, user, password, database, charset='utf8'):
        """
        初始化MySQL连接参数
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.charset = charset
        self.connection = None
        self.cursor = None
    
    def connect(self):
        """建立MySQL连接"""
        try:
            self.connection = MySQLdb.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                passwd=self.password,
                db=self.database,
                charset=self.charset
            )
            self.cursor = self.connection.cursor()
            print("[%s] 成功连接到MySQL数据库: %s@%s:%s/%s" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                self.user, self.host, self.port, self.database
            ))
            return True
        except MySQLdb.Error as e:
            print("[%s] 连接MySQL失败: %s" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e)
            ))
            return False
    
    def disconnect(self):
        """关闭MySQL连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("[%s] 已断开MySQL连接" % (
            datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ))
    
    def execute_sql(self, sql):
        """
        执行单个SQL语句
        
        Returns:
            tuple: (是否成功, 结果/错误信息)
        """
        try:
            self.cursor.execute(sql)
            
            # 如果是SELECT语句，获取结果
            if sql.strip().upper().startswith('SELECT'):
                results = self.cursor.fetchall()
                return True, results
            else:
                # 对于ALTER等DDL语句，提交事务
                self.connection.commit()
                return True, "执行成功，影响行数: %s" % self.cursor.rowcount
                
        except MySQLdb.Error as e:
            # 回滚事务
            self.connection.rollback()
            return False, str(e)
    
    def read_sql_file(self, file_path):
        """
        读取SQL文件并返回有效的SQL语句列表
        
        Returns:
            list: SQL语句列表
        """
        sql_statements = []
        
        if not os.path.exists(file_path):
            print("[%s] 错误: SQL文件不存在: %s" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'), file_path
            ))
            return sql_statements
        
        try:
            # 使用codecs.open来处理UTF-8编码，兼容Python 2.7
            with codecs.open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    # 去除首尾空白字符
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith('--'):
                        continue
                    
                    # 移除行末注释
                    if '--' in line:
                        line = line.split('--')[0].strip()
                    
                    # 确保SQL语句以分号结尾
                    if line and not line.endswith(';'):
                        line += ';'
                    
                    if line and line != ';':
                        sql_statements.append((line_num, line))
            
            print("[%s] 成功读取SQL文件: %s，共%s条有效SQL语句" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                file_path, len(sql_statements)
            ))
            
        except Exception as e:
            print("[%s] 读取SQL文件失败: %s" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e)
            ))
        
        return sql_statements
    
    def execute_sql_file(self, file_path, delay_seconds=60):
        """
        执行SQL文件中的所有语句，每个语句之间暂停指定时间
        """
        # 读取SQL文件
        sql_statements = self.read_sql_file(file_path)
        
        if not sql_statements:
            print("[%s] 没有找到有效的SQL语句" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            return
        
        # 建立数据库连接
        if not self.connect():
            return
        
        success_count = 0
        error_count = 0
        
        try:
            for i, (line_num, sql) in enumerate(sql_statements, 1):
                print("\n" + "="*80)
                print("[%s] 执行第 %s/%s 个SQL语句 (文件第%s行):" % (
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    i, len(sql_statements), line_num
                ))
                print("SQL: %s" % sql)
                
                # 执行SQL语句
                success, result = self.execute_sql(sql)
                
                if success:
                    success_count += 1
                    print("[%s] ✓ 执行成功" % (
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    ))
                    
                    # 如果是SELECT语句，显示结果
                    if sql.strip().upper().startswith('SELECT'):
                        if result:
                            print("查询结果:")
                            for row in result:
                                print("  %s" % str(row))
                        else:
                            print("查询结果: 无数据")
                    else:
                        print("结果: %s" % result)
                else:
                    error_count += 1
                    print("[%s] ✗ 执行失败: %s" % (
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'), result
                    ))
                
                # 如果不是最后一个SQL语句，则暂停
                if i < len(sql_statements):
                    print("[%s] 等待 %s 秒后执行下一个SQL..." % (
                        datetime.now().strftime('%Y-%m-%d %H:%M:%S'), delay_seconds
                    ))
                    time.sleep(delay_seconds)
        
        except KeyboardInterrupt:
            print("\n[%s] 用户中断执行" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
        
        finally:
            # 关闭数据库连接
            self.disconnect()
            
            # 输出执行统计
            print("\n" + "="*80)
            print("[%s] 执行完成统计:" % (
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))
            print("  总SQL语句数: %s" % len(sql_statements))
            print("  成功执行: %s" % success_count)
            print("  执行失败: %s" % error_count)


def main():
    """主函数"""
    # MySQL连接配置 - 请根据实际情况修改这些参数
    config = {
        'host': 'localhost',        # MySQL服务器地址
        'port': 3306,              # MySQL端口号
        'user': 'root',            # 用户名
        'password': 'your_password', # 密码 - 请修改为实际密码
        'database': 'your_database', # 数据库名 - 请修改为实际数据库名
        'charset': 'utf8'
    }
    
    # SQL文件路径
    sql_file_path = r'C:\Users\<USER>\PycharmProjects\pythonProject\sqlscript\alter_statements-第一批.sql'
    
    # 每个SQL语句之间的延迟时间（秒）
    delay_seconds = 60
    
    print("MySQL SQL脚本执行器 - Python 2.7版本")
    print("="*50)
    print("SQL文件: %s" % sql_file_path)
    print("延迟时间: %s 秒" % delay_seconds)
    print("数据库: %s@%s:%s/%s" % (
        config['user'], config['host'], config['port'], config['database']
    ))
    print("="*50)
    
    # 确认执行
    try:
        confirm = raw_input("确认执行吗？(y/N): ")
    except KeyboardInterrupt:
        print("\n用户取消执行")
        return
    
    if confirm.lower() != 'y':
        print("用户取消执行")
        return
    
    # 创建执行器并执行
    executor = MySQLExecutor(**config)
    executor.execute_sql_file(sql_file_path, delay_seconds)


if __name__ == '__main__':
    main()
