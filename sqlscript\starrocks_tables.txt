SHOW CREATE TABLE AccountsPayCloseCycle ;
SHOW CREATE TABLE AccountsPayableRate ;
SHOW CREATE TABLE AccountsReceivableRate ;
SHOW CREATE TABLE AccountsReceiveRate ;
SHOW CREATE TABLE AdvancePaymentPeriod ;
SHOW CREATE TABLE BOMAuditcycle ;
SHOW CREATE TABLE BOMRateOfaudit ;
SHOW CREATE TABLE BankAccounts ;
SHOW CREATE TABLE BankExpenseRate ;
SHOW CREATE TABLE BankIncomeRate ;
SHOW CREATE TABLE BankReceiptsNum ;
SHOW CREATE TABLE BillsReceiveNum ;
SHOW CREATE TABLE CollectVerificationCycle ;
SHOW CREATE TABLE CollectVerificationRate ;
SHOW CREATE TABLE CompleteOnTimeRate ;
SHOW CREATE TABLE CompleteRateOfWork ;
SHOW CREATE TABLE CostCompositionRatio ;
SHOW CREATE TABLE DaysPayableOutstanding ;
SHOW CREATE TABLE DeliveryRateForProcurement ;
SHOW CREATE TABLE DeliveryRateOfOrders ;
SHOW CREATE TABLE DeviationRateBetweenOrderPrice ;
SHOW CREATE TABLE DeviationRatePurchasePrice ;
SHOW CREATE TABLE DocumentDeductionOnTime ;
SHOW CREATE TABLE DocumentDeductionTimely ;
SHOW CREATE TABLE DocumentQuantity ;
SHOW CREATE TABLE DocumentReplenishmentRate ;
SHOW CREATE TABLE ECNAuditcycle ;
SHOW CREATE TABLE ECNRateOfaudit ;
SHOW CREATE TABLE ECNReplaceNumber ;
SHOW CREATE TABLE Financialreplenishmentrate ;
SHOW CREATE TABLE IncreaseInMaterials ;
SHOW CREATE TABLE InventoryTurnover ;
SHOW CREATE TABLE InventoryTurnoverProduct ;
SHOW CREATE TABLE InvoicingVolume ;
SHOW CREATE TABLE LeadTimesRate ;
SHOW CREATE TABLE MISCAccountSubjects ;
SHOW CREATE TABLE MISCReasonCode ;
SHOW CREATE TABLE MaterialInventoryTurnoverDay ;
SHOW CREATE TABLE MaterialIssuanceCycle ;
SHOW CREATE TABLE MissingrateOfWorkHour ;
SHOW CREATE TABLE MissingworkRate ;
SHOW CREATE TABLE ModuleMonthlyDays ;
SHOW CREATE TABLE MonthlyIncreaseInPurchase ;
SHOW CREATE TABLE NumOfBankPay ;
SHOW CREATE TABLE NumberOfBillsPay ;
SHOW CREATE TABLE NumberOfOrderChanges ;
SHOW CREATE TABLE OrderChangeCoverageRate ;
SHOW CREATE TABLE OrderIncrease ;
SHOW CREATE TABLE OrderLeadTime ;
SHOW CREATE TABLE OrdertoWork ;
SHOW CREATE TABLE OverdueAccountsPayable ;
SHOW CREATE TABLE OverdueAccountsReceiveRate ;
SHOW CREATE TABLE PayableReconciliationRate ;
SHOW CREATE TABLE PaymentApprovalCycle ;
SHOW CREATE TABLE PaymentVerificationCycle ;
SHOW CREATE TABLE ProcurementCycle ;
SHOW CREATE TABLE ProcurementSourcesRate ;
SHOW CREATE TABLE ProcurementTypeDeliveryRate ;
SHOW CREATE TABLE ProductInventoryTurnover ;
SHOW CREATE TABLE ProductProductionCycle ;
SHOW CREATE TABLE ProductionCycle ;
SHOW CREATE TABLE ProductionSourceRate ;
SHOW CREATE TABLE ProductionYield ;
SHOW CREATE TABLE PurchaseQualityDefectRate ;
SHOW CREATE TABLE PurchaseZeroUnitPriceRatio ;
SHOW CREATE TABLE RateBetweenInvoiceAndPurchase ;
SHOW CREATE TABLE RateBetweenInvoiceOrder ;
SHOW CREATE TABLE RateOfOrderChange ;
SHOW CREATE TABLE RateOfPreparationLeadTime ;
SHOW CREATE TABLE ReceivableTurnoverRate ;
SHOW CREATE TABLE SemiFinishProductProductionCycle ;
SHOW CREATE TABLE StartRateOfWork ;
SHOW CREATE TABLE StartWorkRate ;
SHOW CREATE TABLE TimelyCompletionOfWork ;
SHOW CREATE TABLE TransactionOfBillsPay ;
SHOW CREATE TABLE TransactionsOfNotesReceive ;
SHOW CREATE TABLE WorkOrderReportRateBatch ;
SHOW CREATE TABLE WorkOrderReportRateSingle ;
