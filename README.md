# MySQL SQL脚本执行器

这是一个专为Python 2.7.5设计的MySQL SQL脚本执行器，可以逐行执行SQL文件中的语句，并在每个语句之间暂停指定时间。

## 功能特点

- ✅ 兼容Python 2.7.5
- ✅ 逐行执行SQL语句
- ✅ 每个SQL语句之间可设置延迟时间（默认60秒）
- ✅ 支持ALTER TABLE和SELECT语句
- ✅ 自动跳过注释和空行
- ✅ 详细的执行日志
- ✅ 错误处理和事务回滚
- ✅ 执行统计报告

## 环境要求

1. **Python 2.7.5**
2. **MySQL-python模块** (MySQLdb)

### 安装MySQL-python模块

```bash
# 对于Python 2.7，使用pip安装
pip install MySQL-python

# 或者在某些系统上
pip2 install MySQL-python
```

## 文件说明

- `mysql_executor_py27.py` - 主执行程序（Python 2.7专用版本）
- `config.py` - 配置文件（可选）
- `README.md` - 使用说明

## 使用方法

### 1. 修改数据库连接配置

编辑 `mysql_executor_py27.py` 文件中的配置部分：

```python
config = {
    'host': 'localhost',        # MySQL服务器地址
    'port': 3306,              # MySQL端口号
    'user': 'root',            # 用户名
    'password': 'your_password', # 密码 - 请修改为实际密码
    'database': 'your_database', # 数据库名 - 请修改为实际数据库名
    'charset': 'utf8'
}
```

### 2. 运行程序

```bash
python mysql_executor_py27.py
```

### 3. 确认执行

程序会显示配置信息并要求确认：

```
MySQL SQL脚本执行器 - Python 2.7版本
==================================================
SQL文件: C:\Users\<USER>\PycharmProjects\pythonProject\sqlscript\alter_statements-第一批.sql
延迟时间: 60 秒
数据库: root@localhost:3306/your_database
==================================================
确认执行吗？(y/N): 
```

输入 `y` 确认执行。

## 执行过程

程序会：

1. 读取SQL文件并解析有效的SQL语句
2. 建立MySQL数据库连接
3. 逐个执行SQL语句：
   - 显示当前执行的SQL语句
   - 执行SQL并显示结果
   - 等待指定时间（默认60秒）后执行下一个
4. 显示执行统计报告

## 示例输出

```
[2025-06-25 14:30:15] 成功连接到MySQL数据库: root@localhost:3306/test_db
[2025-06-25 14:30:15] 成功读取SQL文件: alter_statements-第一批.sql，共176条有效SQL语句

================================================================================
[2025-06-25 14:30:15] 执行第 1/176 个SQL语句 (文件第5行):
SQL: ALTER TABLE AccountsPayCloseCycle modify COLUMN SettlementCycle decimal(38,6) NULL;
[2025-06-25 14:30:15] ✓ 执行成功
结果: 执行成功，影响行数: 0
[2025-06-25 14:30:15] 等待 60 秒后执行下一个SQL...

================================================================================
[2025-06-25 14:31:15] 执行第 2/176 个SQL语句 (文件第6行):
SQL: ALTER TABLE AccountsPayCloseCycle modify COLUMN SettlementDays decimal(38,6) NULL;
[2025-06-25 14:31:15] ✓ 执行成功
结果: 执行成功，影响行数: 0
[2025-06-25 14:31:15] 等待 60 秒后执行下一个SQL...
```

## 注意事项

1. **备份数据库**：执行ALTER语句前请务必备份数据库
2. **测试环境**：建议先在测试环境中验证脚本
3. **网络连接**：确保网络连接稳定，避免长时间执行过程中断线
4. **权限检查**：确保数据库用户有足够的权限执行ALTER语句
5. **中断执行**：可以使用 Ctrl+C 中断执行

## 自定义配置

### 修改延迟时间

在 `main()` 函数中修改 `delay_seconds` 变量：

```python
delay_seconds = 30  # 改为30秒延迟
```

### 修改SQL文件路径

在 `main()` 函数中修改 `sql_file_path` 变量：

```python
sql_file_path = r'path\to\your\sql\file.sql'
```

## 故障排除

### 1. 连接失败
- 检查MySQL服务是否运行
- 验证连接参数（主机、端口、用户名、密码）
- 确认数据库存在

### 2. 模块导入错误
```
ImportError: No module named MySQLdb
```
解决方法：安装MySQL-python模块
```bash
pip install MySQL-python
```

### 3. 编码问题
如果遇到中文字符问题，确保：
- SQL文件保存为UTF-8编码
- MySQL数据库字符集设置正确

### 4. 权限问题
```
Access denied for user
```
解决方法：确保数据库用户有ALTER权限
```sql
GRANT ALTER ON database_name.* TO 'username'@'host';
```

## 支持的SQL语句类型

- ✅ ALTER TABLE 语句
- ✅ SELECT 语句
- ✅ 其他DDL语句（CREATE, DROP等）
- ✅ DML语句（INSERT, UPDATE, DELETE等）

## 版本历史

- v1.0 - 初始版本，支持Python 2.7.5
