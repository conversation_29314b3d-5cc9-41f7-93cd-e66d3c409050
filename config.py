# -*- coding: utf-8 -*-
"""
MySQL连接配置文件
请根据实际情况修改以下配置
"""

# MySQL数据库连接配置
MYSQL_CONFIG = {
    'host': 'localhost',        # MySQL服务器地址
    'port': 3306,              # MySQL端口号
    'user': 'root',            # 用户名
    'password': 'your_password', # 密码 - 请修改为实际密码
    'database': 'your_database', # 数据库名 - 请修改为实际数据库名
    'charset': 'utf8'          # 字符编码
}

# SQL文件路径
SQL_FILE_PATH = r'C:\Users\<USER>\PycharmProjects\pythonProject\sqlscript\alter_statements-第一批.sql'

# 每个SQL语句之间的延迟时间（秒）
DELAY_SECONDS = 60
