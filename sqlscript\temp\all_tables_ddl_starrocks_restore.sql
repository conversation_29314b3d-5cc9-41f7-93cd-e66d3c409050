-- 数据还原语句
-- 生成时间: 2025-06-25
-- 注意：执行前请确保备份表存在

ALTER TABLE `AccountsPayCloseCycle` RENAME TO `AccountsPayCloseCycle_temp`;
ALTER TABLE `AccountsPayCloseCycle_20250625_bak` RENAME TO `AccountsPayCloseCycle`;
ALTER TABLE `AccountsPayableRate` RENAME TO `AccountsPayableRate_temp`;
ALTER TABLE `AccountsPayableRate_20250625_bak` RENAME TO `AccountsPayableRate`;
ALTER TABLE `AccountsReceivableRate` RENAME TO `AccountsReceivableRate_temp`;
ALTER TABLE `AccountsReceivableRate_20250625_bak` RENAME TO `AccountsReceivableRate`;
ALTER TABLE `AccountsReceiveRate` RENAME TO `AccountsReceiveRate_temp`;
ALTER TABLE `AccountsReceiveRate_20250625_bak` RENAME TO `AccountsReceiveRate`;
ALTER TABLE `AdvancePaymentPeriod` RENAME TO `AdvancePaymentPeriod_temp`;
ALTER TABLE `AdvancePaymentPeriod_20250625_bak` RENAME TO `AdvancePaymentPeriod`;
ALTER TABLE `BOMAuditcycle` RENAME TO `BOMAuditcycle_temp`;
ALTER TABLE `BOMAuditcycle_20250625_bak` RENAME TO `BOMAuditcycle`;
ALTER TABLE `BOMRateOfaudit` RENAME TO `BOMRateOfaudit_temp`;
ALTER TABLE `BOMRateOfaudit_20250625_bak` RENAME TO `BOMRateOfaudit`;
ALTER TABLE `BankAccounts` RENAME TO `BankAccounts_temp`;
ALTER TABLE `BankAccounts_20250625_bak` RENAME TO `BankAccounts`;
ALTER TABLE `BankExpenseRate` RENAME TO `BankExpenseRate_temp`;
ALTER TABLE `BankExpenseRate_20250625_bak` RENAME TO `BankExpenseRate`;
ALTER TABLE `BankIncomeRate` RENAME TO `BankIncomeRate_temp`;
ALTER TABLE `BankIncomeRate_20250625_bak` RENAME TO `BankIncomeRate`;
ALTER TABLE `BankReceiptsNum` RENAME TO `BankReceiptsNum_temp`;
ALTER TABLE `BankReceiptsNum_20250625_bak` RENAME TO `BankReceiptsNum`;
ALTER TABLE `BillsReceiveNum` RENAME TO `BillsReceiveNum_temp`;
ALTER TABLE `BillsReceiveNum_20250625_bak` RENAME TO `BillsReceiveNum`;
ALTER TABLE `CollectVerificationCycle` RENAME TO `CollectVerificationCycle_temp`;
ALTER TABLE `CollectVerificationCycle_20250625_bak` RENAME TO `CollectVerificationCycle`;
ALTER TABLE `CollectVerificationRate` RENAME TO `CollectVerificationRate_temp`;
ALTER TABLE `CollectVerificationRate_20250625_bak` RENAME TO `CollectVerificationRate`;
ALTER TABLE `CompleteOnTimeRate` RENAME TO `CompleteOnTimeRate_temp`;
ALTER TABLE `CompleteOnTimeRate_20250625_bak` RENAME TO `CompleteOnTimeRate`;
ALTER TABLE `CompleteRateOfWork` RENAME TO `CompleteRateOfWork_temp`;
ALTER TABLE `CompleteRateOfWork_20250625_bak` RENAME TO `CompleteRateOfWork`;
ALTER TABLE `CostCompositionRatio` RENAME TO `CostCompositionRatio_temp`;
ALTER TABLE `CostCompositionRatio_20250625_bak` RENAME TO `CostCompositionRatio`;
ALTER TABLE `DaysPayableOutstanding` RENAME TO `DaysPayableOutstanding_temp`;
ALTER TABLE `DaysPayableOutstanding_20250625_bak` RENAME TO `DaysPayableOutstanding`;
ALTER TABLE `DeliveryRateForProcurement` RENAME TO `DeliveryRateForProcurement_temp`;
ALTER TABLE `DeliveryRateForProcurement_20250625_bak` RENAME TO `DeliveryRateForProcurement`;
ALTER TABLE `DeliveryRateOfOrders` RENAME TO `DeliveryRateOfOrders_temp`;
ALTER TABLE `DeliveryRateOfOrders_20250625_bak` RENAME TO `DeliveryRateOfOrders`;
ALTER TABLE `DeviationRateBetweenOrderPrice` RENAME TO `DeviationRateBetweenOrderPrice_temp`;
ALTER TABLE `DeviationRateBetweenOrderPrice_20250625_bak` RENAME TO `DeviationRateBetweenOrderPrice`;
ALTER TABLE `DeviationRatePurchasePrice` RENAME TO `DeviationRatePurchasePrice_temp`;
ALTER TABLE `DeviationRatePurchasePrice_20250625_bak` RENAME TO `DeviationRatePurchasePrice`;
ALTER TABLE `DocumentDeductionOnTime` RENAME TO `DocumentDeductionOnTime_temp`;
ALTER TABLE `DocumentDeductionOnTime_20250625_bak` RENAME TO `DocumentDeductionOnTime`;
ALTER TABLE `DocumentDeductionTimely` RENAME TO `DocumentDeductionTimely_temp`;
ALTER TABLE `DocumentDeductionTimely_20250625_bak` RENAME TO `DocumentDeductionTimely`;
ALTER TABLE `DocumentQuantity` RENAME TO `DocumentQuantity_temp`;
ALTER TABLE `DocumentQuantity_20250625_bak` RENAME TO `DocumentQuantity`;
ALTER TABLE `DocumentReplenishmentRate` RENAME TO `DocumentReplenishmentRate_temp`;
ALTER TABLE `DocumentReplenishmentRate_20250625_bak` RENAME TO `DocumentReplenishmentRate`;
ALTER TABLE `ECNAuditcycle` RENAME TO `ECNAuditcycle_temp`;
ALTER TABLE `ECNAuditcycle_20250625_bak` RENAME TO `ECNAuditcycle`;
ALTER TABLE `ECNRateOfaudit` RENAME TO `ECNRateOfaudit_temp`;
ALTER TABLE `ECNRateOfaudit_20250625_bak` RENAME TO `ECNRateOfaudit`;
ALTER TABLE `ECNReplaceNumber` RENAME TO `ECNReplaceNumber_temp`;
ALTER TABLE `ECNReplaceNumber_20250625_bak` RENAME TO `ECNReplaceNumber`;
ALTER TABLE `Financialreplenishmentrate` RENAME TO `Financialreplenishmentrate_temp`;
ALTER TABLE `Financialreplenishmentrate_20250625_bak` RENAME TO `Financialreplenishmentrate`;
ALTER TABLE `IncreaseInMaterials` RENAME TO `IncreaseInMaterials_temp`;
ALTER TABLE `IncreaseInMaterials_20250625_bak` RENAME TO `IncreaseInMaterials`;
ALTER TABLE `InventoryTurnover` RENAME TO `InventoryTurnover_temp`;
ALTER TABLE `InventoryTurnover_20250625_bak` RENAME TO `InventoryTurnover`;
ALTER TABLE `InventoryTurnoverProduct` RENAME TO `InventoryTurnoverProduct_temp`;
ALTER TABLE `InventoryTurnoverProduct_20250625_bak` RENAME TO `InventoryTurnoverProduct`;
ALTER TABLE `InvoicingVolume` RENAME TO `InvoicingVolume_temp`;
ALTER TABLE `InvoicingVolume_20250625_bak` RENAME TO `InvoicingVolume`;
ALTER TABLE `LeadTimesRate` RENAME TO `LeadTimesRate_temp`;
ALTER TABLE `LeadTimesRate_20250625_bak` RENAME TO `LeadTimesRate`;
ALTER TABLE `MISCAccountSubjects` RENAME TO `MISCAccountSubjects_temp`;
ALTER TABLE `MISCAccountSubjects_20250625_bak` RENAME TO `MISCAccountSubjects`;
ALTER TABLE `MISCReasonCode` RENAME TO `MISCReasonCode_temp`;
ALTER TABLE `MISCReasonCode_20250625_bak` RENAME TO `MISCReasonCode`;
ALTER TABLE `MaterialInventoryTurnoverDay` RENAME TO `MaterialInventoryTurnoverDay_temp`;
ALTER TABLE `MaterialInventoryTurnoverDay_20250625_bak` RENAME TO `MaterialInventoryTurnoverDay`;
ALTER TABLE `MaterialIssuanceCycle` RENAME TO `MaterialIssuanceCycle_temp`;
ALTER TABLE `MaterialIssuanceCycle_20250625_bak` RENAME TO `MaterialIssuanceCycle`;
ALTER TABLE `MissingrateOfWorkHour` RENAME TO `MissingrateOfWorkHour_temp`;
ALTER TABLE `MissingrateOfWorkHour_20250625_bak` RENAME TO `MissingrateOfWorkHour`;
ALTER TABLE `MissingworkRate` RENAME TO `MissingworkRate_temp`;
ALTER TABLE `MissingworkRate_20250625_bak` RENAME TO `MissingworkRate`;
ALTER TABLE `ModuleMonthlyDays` RENAME TO `ModuleMonthlyDays_temp`;
ALTER TABLE `ModuleMonthlyDays_20250625_bak` RENAME TO `ModuleMonthlyDays`;
ALTER TABLE `MonthlyIncreaseInPurchase` RENAME TO `MonthlyIncreaseInPurchase_temp`;
ALTER TABLE `MonthlyIncreaseInPurchase_20250625_bak` RENAME TO `MonthlyIncreaseInPurchase`;
ALTER TABLE `NumOfBankPay` RENAME TO `NumOfBankPay_temp`;
ALTER TABLE `NumOfBankPay_20250625_bak` RENAME TO `NumOfBankPay`;
ALTER TABLE `NumberOfBillsPay` RENAME TO `NumberOfBillsPay_temp`;
ALTER TABLE `NumberOfBillsPay_20250625_bak` RENAME TO `NumberOfBillsPay`;
ALTER TABLE `NumberOfOrderChanges` RENAME TO `NumberOfOrderChanges_temp`;
ALTER TABLE `NumberOfOrderChanges_20250625_bak` RENAME TO `NumberOfOrderChanges`;
ALTER TABLE `OrderChangeCoverageRate` RENAME TO `OrderChangeCoverageRate_temp`;
ALTER TABLE `OrderChangeCoverageRate_20250625_bak` RENAME TO `OrderChangeCoverageRate`;
ALTER TABLE `OrderIncrease` RENAME TO `OrderIncrease_temp`;
ALTER TABLE `OrderIncrease_20250625_bak` RENAME TO `OrderIncrease`;
ALTER TABLE `OrderLeadTime` RENAME TO `OrderLeadTime_temp`;
ALTER TABLE `OrderLeadTime_20250625_bak` RENAME TO `OrderLeadTime`;
ALTER TABLE `OrdertoWork` RENAME TO `OrdertoWork_temp`;
ALTER TABLE `OrdertoWork_20250625_bak` RENAME TO `OrdertoWork`;
ALTER TABLE `OverdueAccountsPayable` RENAME TO `OverdueAccountsPayable_temp`;
ALTER TABLE `OverdueAccountsPayable_20250625_bak` RENAME TO `OverdueAccountsPayable`;
ALTER TABLE `OverdueAccountsReceiveRate` RENAME TO `OverdueAccountsReceiveRate_temp`;
ALTER TABLE `OverdueAccountsReceiveRate_20250625_bak` RENAME TO `OverdueAccountsReceiveRate`;
ALTER TABLE `PayableReconciliationRate` RENAME TO `PayableReconciliationRate_temp`;
ALTER TABLE `PayableReconciliationRate_20250625_bak` RENAME TO `PayableReconciliationRate`;
ALTER TABLE `PaymentApprovalCycle` RENAME TO `PaymentApprovalCycle_temp`;
ALTER TABLE `PaymentApprovalCycle_20250625_bak` RENAME TO `PaymentApprovalCycle`;
ALTER TABLE `PaymentVerificationCycle` RENAME TO `PaymentVerificationCycle_temp`;
ALTER TABLE `PaymentVerificationCycle_20250625_bak` RENAME TO `PaymentVerificationCycle`;
ALTER TABLE `ProcurementCycle` RENAME TO `ProcurementCycle_temp`;
ALTER TABLE `ProcurementCycle_20250625_bak` RENAME TO `ProcurementCycle`;
ALTER TABLE `ProcurementSourcesRate` RENAME TO `ProcurementSourcesRate_temp`;
ALTER TABLE `ProcurementSourcesRate_20250625_bak` RENAME TO `ProcurementSourcesRate`;
ALTER TABLE `ProcurementTypeDeliveryRate` RENAME TO `ProcurementTypeDeliveryRate_temp`;
ALTER TABLE `ProcurementTypeDeliveryRate_20250625_bak` RENAME TO `ProcurementTypeDeliveryRate`;
ALTER TABLE `ProductInventoryTurnover` RENAME TO `ProductInventoryTurnover_temp`;
ALTER TABLE `ProductInventoryTurnover_20250625_bak` RENAME TO `ProductInventoryTurnover`;
ALTER TABLE `ProductProductionCycle` RENAME TO `ProductProductionCycle_temp`;
ALTER TABLE `ProductProductionCycle_20250625_bak` RENAME TO `ProductProductionCycle`;
ALTER TABLE `ProductionCycle` RENAME TO `ProductionCycle_temp`;
ALTER TABLE `ProductionCycle_20250625_bak` RENAME TO `ProductionCycle`;
ALTER TABLE `ProductionSourceRate` RENAME TO `ProductionSourceRate_temp`;
ALTER TABLE `ProductionSourceRate_20250625_bak` RENAME TO `ProductionSourceRate`;
ALTER TABLE `ProductionYield` RENAME TO `ProductionYield_temp`;
ALTER TABLE `ProductionYield_20250625_bak` RENAME TO `ProductionYield`;
ALTER TABLE `PurchaseQualityDefectRate` RENAME TO `PurchaseQualityDefectRate_temp`;
ALTER TABLE `PurchaseQualityDefectRate_20250625_bak` RENAME TO `PurchaseQualityDefectRate`;
ALTER TABLE `PurchaseZeroUnitPriceRatio` RENAME TO `PurchaseZeroUnitPriceRatio_temp`;
ALTER TABLE `PurchaseZeroUnitPriceRatio_20250625_bak` RENAME TO `PurchaseZeroUnitPriceRatio`;
ALTER TABLE `RateBetweenInvoiceAndPurchase` RENAME TO `RateBetweenInvoiceAndPurchase_temp`;
ALTER TABLE `RateBetweenInvoiceAndPurchase_20250625_bak` RENAME TO `RateBetweenInvoiceAndPurchase`;
ALTER TABLE `RateBetweenInvoiceOrder` RENAME TO `RateBetweenInvoiceOrder_temp`;
ALTER TABLE `RateBetweenInvoiceOrder_20250625_bak` RENAME TO `RateBetweenInvoiceOrder`;
ALTER TABLE `RateOfOrderChange` RENAME TO `RateOfOrderChange_temp`;
ALTER TABLE `RateOfOrderChange_20250625_bak` RENAME TO `RateOfOrderChange`;
ALTER TABLE `RateOfPreparationLeadTime` RENAME TO `RateOfPreparationLeadTime_temp`;
ALTER TABLE `RateOfPreparationLeadTime_20250625_bak` RENAME TO `RateOfPreparationLeadTime`;
ALTER TABLE `ReceivableTurnoverRate` RENAME TO `ReceivableTurnoverRate_temp`;
ALTER TABLE `ReceivableTurnoverRate_20250625_bak` RENAME TO `ReceivableTurnoverRate`;
ALTER TABLE `SemiFinishProductProductionCycle` RENAME TO `SemiFinishProductProductionCycle_temp`;
ALTER TABLE `SemiFinishProductProductionCycle_20250625_bak` RENAME TO `SemiFinishProductProductionCycle`;
ALTER TABLE `StartRateOfWork` RENAME TO `StartRateOfWork_temp`;
ALTER TABLE `StartRateOfWork_20250625_bak` RENAME TO `StartRateOfWork`;
ALTER TABLE `StartWorkRate` RENAME TO `StartWorkRate_temp`;
ALTER TABLE `StartWorkRate_20250625_bak` RENAME TO `StartWorkRate`;
ALTER TABLE `TimelyCompletionOfWork` RENAME TO `TimelyCompletionOfWork_temp`;
ALTER TABLE `TimelyCompletionOfWork_20250625_bak` RENAME TO `TimelyCompletionOfWork`;
ALTER TABLE `TransactionOfBillsPay` RENAME TO `TransactionOfBillsPay_temp`;
ALTER TABLE `TransactionOfBillsPay_20250625_bak` RENAME TO `TransactionOfBillsPay`;
ALTER TABLE `TransactionsOfNotesReceive` RENAME TO `TransactionsOfNotesReceive_temp`;
ALTER TABLE `TransactionsOfNotesReceive_20250625_bak` RENAME TO `TransactionsOfNotesReceive`;
ALTER TABLE `WorkOrderReportRateBatch` RENAME TO `WorkOrderReportRateBatch_temp`;
ALTER TABLE `WorkOrderReportRateBatch_20250625_bak` RENAME TO `WorkOrderReportRateBatch`;
ALTER TABLE `WorkOrderReportRateSingle` RENAME TO `WorkOrderReportRateSingle_temp`;
ALTER TABLE `WorkOrderReportRateSingle_20250625_bak` RENAME TO `WorkOrderReportRateSingle`;
