# -*- coding: utf-8 -*-
"""
MySQL连接配置示例文件
复制此文件为config.py并修改相应配置
"""

# MySQL数据库连接配置
MYSQL_CONFIG = {
    'host': 'localhost',           # MySQL服务器地址，如：'*************'
    'port': 3306,                 # MySQL端口号，通常为3306
    'user': 'root',               # 数据库用户名
    'password': 'your_password',   # 数据库密码 - 请修改为实际密码
    'database': 'your_database',   # 数据库名 - 请修改为实际数据库名
    'charset': 'utf8'             # 字符编码，建议使用utf8
}

# SQL文件路径
SQL_FILE_PATH = r'C:\Users\<USER>\PycharmProjects\pythonProject\sqlscript\alter_statements-第一批.sql'

# 每个SQL语句之间的延迟时间（秒）
# 60 = 1分钟，30 = 30秒，120 = 2分钟
DELAY_SECONDS = 60

# 使用说明：
# 1. 将此文件复制为 config.py
# 2. 修改上述配置参数
# 3. 运行 mysql_executor_py27.py
