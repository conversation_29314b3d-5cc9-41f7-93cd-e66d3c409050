# -*- coding: utf-8 -*-
"""
MySQL连接测试脚本 - Python 2.7版本
用于测试数据库连接是否正常
"""

import MySQLdb
import sys
from datetime import datetime

def test_mysql_connection():
    """测试MySQL连接"""
    
    # 数据库连接配置 - 请修改为实际配置
    config = {
        'host': 'localhost',        # MySQL服务器地址
        'port': 3306,              # MySQL端口号
        'user': 'root',            # 用户名
        'password': 'your_password', # 密码 - 请修改为实际密码
        'database': 'your_database', # 数据库名 - 请修改为实际数据库名
        'charset': 'utf8'
    }
    
    print("MySQL连接测试")
    print("="*50)
    print("服务器: %s:%s" % (config['host'], config['port']))
    print("用户名: %s" % config['user'])
    print("数据库: %s" % config['database'])
    print("字符集: %s" % config['charset'])
    print("="*50)
    
    connection = None
    cursor = None
    
    try:
        print("[%s] 正在连接MySQL..." % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 建立连接
        connection = MySQLdb.connect(
            host=config['host'],
            port=config['port'],
            user=config['user'],
            passwd=config['password'],
            db=config['database'],
            charset=config['charset']
        )
        
        cursor = connection.cursor()
        
        print("[%s] ✓ 连接成功！" % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        # 测试基本查询
        print("[%s] 测试基本查询..." % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print("MySQL版本: %s" % version[0])
        
        # 测试数据库信息
        cursor.execute("SELECT DATABASE()")
        current_db = cursor.fetchone()
        print("当前数据库: %s" % current_db[0])
        
        # 测试字符集
        cursor.execute("SHOW VARIABLES LIKE 'character_set_database'")
        charset_info = cursor.fetchone()
        print("数据库字符集: %s" % charset_info[1])
        
        # 测试表权限（尝试查看表列表）
        print("[%s] 测试表访问权限..." % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print("数据库中的表数量: %s" % len(tables))
        
        if len(tables) > 0:
            print("前5个表:")
            for i, table in enumerate(tables[:5]):
                print("  %s. %s" % (i+1, table[0]))
        
        print("[%s] ✓ 所有测试通过！数据库连接正常。" % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        return True
        
    except MySQLdb.Error as e:
        print("[%s] ✗ 连接失败: %s" % (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e)))
        
        # 提供常见错误的解决建议
        error_msg = str(e).lower()
        print("\n故障排除建议:")
        
        if 'access denied' in error_msg:
            print("- 检查用户名和密码是否正确")
            print("- 确认用户是否有访问该数据库的权限")
        elif 'unknown database' in error_msg:
            print("- 检查数据库名是否正确")
            print("- 确认数据库是否存在")
        elif 'can\'t connect' in error_msg or 'connection refused' in error_msg:
            print("- 检查MySQL服务是否运行")
            print("- 检查主机地址和端口是否正确")
            print("- 检查防火墙设置")
        elif 'host' in error_msg and 'not allowed' in error_msg:
            print("- 检查MySQL用户的主机访问权限")
            print("- 可能需要为用户添加从当前主机访问的权限")
        
        return False
        
    except Exception as e:
        print("[%s] ✗ 未知错误: %s" % (datetime.now().strftime('%Y-%m-%d %H:%M:%S'), str(e)))
        return False
        
    finally:
        # 关闭连接
        if cursor:
            cursor.close()
        if connection:
            connection.close()
            print("[%s] 连接已关闭" % datetime.now().strftime('%Y-%m-%d %H:%M:%S'))


def main():
    """主函数"""
    print("MySQL连接测试工具 - Python 2.7版本")
    print("请先修改脚本中的数据库连接配置，然后运行测试。\n")
    
    try:
        confirm = raw_input("是否开始测试连接？(y/N): ")
    except KeyboardInterrupt:
        print("\n用户取消测试")
        return
    
    if confirm.lower() != 'y':
        print("用户取消测试")
        return
    
    success = test_mysql_connection()
    
    if success:
        print("\n" + "="*50)
        print("测试结果: 连接成功！可以运行主程序了。")
    else:
        print("\n" + "="*50)
        print("测试结果: 连接失败！请检查配置后重试。")


if __name__ == '__main__':
    main()
