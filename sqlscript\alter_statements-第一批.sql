-- 自动生成的ALTER语句
-- 将decimal(12,2)字段修改为decimal(38,6)
-- 生成时间: 2025-06-25 10:43:18.447233

ALTER TABLE AccountsPayCloseCycle modify COLUMN SettlementCycle decimal(38,6) NULL;
ALTER TABLE AccountsPayCloseCycle modify COLUMN SettlementDays decimal(38,6) NULL;
select * from AccountsPayCloseCycle order by collectedTime desc limit 5;
ALTER TABLE AccountsPayableRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE AccountsPayableRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE AccountsPayableRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from AccountsPayableRate order by collectedTime desc limit 5;
ALTER TABLE AccountsReceivableRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE AccountsReceivableRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE AccountsReceivableRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from AccountsReceivableRate order by collectedTime desc limit 5;
ALTER TABLE AccountsReceiveRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE AccountsReceiveRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE AccountsReceiveRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from AccountsReceiveRate order by collectedTime desc limit 5; -- 下午可以验证数据
ALTER TABLE AdvancePaymentPeriod modify COLUMN advance_payment_period decimal(38,6) NULL;
ALTER TABLE AdvancePaymentPeriod modify COLUMN Prepaid_time decimal(38,6) NULL;
select * from AdvancePaymentPeriod order by collectedTime desc limit 5;
ALTER TABLE BOMAuditcycle modify COLUMN Audit_cycle decimal(38,6) NULL;
ALTER TABLE BOMAuditcycle modify COLUMN Total_audit_time decimal(38,6) NULL;
select * from BOMAuditcycle order by collectedTime desc limit 5;
ALTER TABLE BOMRateOfaudit modify COLUMN RateOfAudit decimal(38,6) NULL;
select * from BOMRateOfaudit order by collectedTime desc limit 5;
ALTER TABLE BankExpenseRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE BankExpenseRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE BankExpenseRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from BankExpenseRate order by collectedTime desc limit 5;
ALTER TABLE BankIncomeRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE BankIncomeRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE BankIncomeRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from BankIncomeRate order by collectedTime desc limit 5;
ALTER TABLE CollectVerificationCycle modify COLUMN Collect_verification_cycle decimal(38,6) NULL;
ALTER TABLE CollectVerificationCycle modify COLUMN Write_off_time decimal(38,6) NULL;
select * from CollectVerificationCycle order by collectedTime desc limit 5;
ALTER TABLE CollectVerificationRate modify COLUMN Collect_verification_rate decimal(38,6) NULL;
select * from CollectVerificationRate order by collectedTime desc limit 5;
ALTER TABLE CompleteOnTimeRate modify COLUMN CompleteOnTimeRate decimal(38,6) NULL;
select * from CompleteOnTimeRate order by collectedTime desc limit 5;
ALTER TABLE CompleteRateOfWork modify COLUMN CompleteOnTimeRate decimal(38,6) NULL;
select * from CompleteRateOfWork order by collectedTime desc limit 5; -- 已经验证
ALTER TABLE CostCompositionRatio modify COLUMN Material_proportion decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Labor_proportion decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Processing_fees decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Production_costs1 decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Production_costs2 decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Production_costs3 decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Production_costs4 decimal(38,6) NULL;
ALTER TABLE CostCompositionRatio modify COLUMN Production_costs5 decimal(38,6) NULL;
select * from CostCompositionRatio order by collectedTime desc limit 5; -- 已经验证
ALTER TABLE DaysPayableOutstanding modify COLUMN Accounts_payable_rate decimal(38,6) NULL;
select * from DaysPayableOutstanding order by collectedTime desc limit 5;
ALTER TABLE DeliveryRateForProcurement modify COLUMN Procurement_delivery_rate decimal(38,6) NULL;
select * from DeliveryRateForProcurement order by collectedTime desc limit 5;
ALTER TABLE DeliveryRateOfOrders modify COLUMN Order_delivery_rate decimal(38,6) NULL;
select * from DeliveryRateOfOrders order by collectedTime desc limit 5;
ALTER TABLE DeviationRateBetweenOrderPrice modify COLUMN Deviation_rate decimal(38,6) NULL;
ALTER TABLE DeviationRateBetweenOrderPrice modify COLUMN Difference_between_order_price decimal(38,6) NULL;
ALTER TABLE DeviationRateBetweenOrderPrice modify COLUMN Evaluated_unit_price decimal(38,6) NULL;
select * from DeviationRateBetweenOrderPrice order by collectedTime desc limit 5;
ALTER TABLE DeviationRatePurchasePrice modify COLUMN Deviation_Rate_Purchase_Price decimal(38,6) NULL;
ALTER TABLE DeviationRatePurchasePrice modify COLUMN Differences_in_procurement decimal(38,6) NULL;
ALTER TABLE DeviationRatePurchasePrice modify COLUMN unit_price decimal(38,6) NULL;
select * from DeviationRatePurchasePrice order by collectedTime desc limit 5;
ALTER TABLE DocumentDeductionOnTime modify COLUMN Proportion_of_timely decimal(38,6) NULL;
select * from DocumentDeductionOnTime order by collectedTime desc limit 5;
ALTER TABLE DocumentDeductionTimely modify COLUMN Timely_deduction_rate decimal(38,6) NULL;
select * from DocumentDeductionTimely order by collectedTime desc limit 5;
ALTER TABLE DocumentReplenishmentRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE DocumentReplenishmentRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE DocumentReplenishmentRate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from DocumentReplenishmentRate order by collectedTime desc limit 5;
ALTER TABLE ECNAuditcycle modify COLUMN Audit_cycle decimal(38,6) NULL;
ALTER TABLE ECNAuditcycle modify COLUMN Total_audit_time decimal(38,6) NULL;
select * from ECNAuditcycle order by collectedTime desc limit 5;
ALTER TABLE ECNRateOfaudit modify COLUMN RateOfAudit decimal(38,6) NULL;
select * from ECNRateOfaudit order by collectedTime desc limit 5;
ALTER TABLE Financialreplenishmentrate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE Financialreplenishmentrate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE Financialreplenishmentrate modify COLUMN Document_review_time decimal(38,6) NULL;
select * from Financialreplenishmentrate order by collectedTime desc limit 5;
ALTER TABLE InventoryTurnover modify COLUMN InventoryTurnoverRate decimal(38,6) NULL;
ALTER TABLE InventoryTurnover modify COLUMN CostofGoods decimal(38,6) NULL;
ALTER TABLE InventoryTurnover modify COLUMN BeginningInventory decimal(38,6) NULL;
ALTER TABLE InventoryTurnover modify COLUMN EndingInventory decimal(38,6) NULL;
ALTER TABLE InventoryTurnover modify COLUMN AverageInventory decimal(38,6) NULL;
select * from InventoryTurnover order by collectedTime desc limit 5;
ALTER TABLE InventoryTurnoverProduct modify COLUMN InventoryTurnoverRate decimal(38,6) NULL;
ALTER TABLE InventoryTurnoverProduct modify COLUMN CostofGoods decimal(38,6) NULL;
ALTER TABLE InventoryTurnoverProduct modify COLUMN BeginningInventory decimal(38,6) NULL;
ALTER TABLE InventoryTurnoverProduct modify COLUMN EndingInventory decimal(38,6) NULL;
ALTER TABLE InventoryTurnoverProduct modify COLUMN AverageInventory decimal(38,6) NULL;
select * from InventoryTurnoverProduct order by collectedTime desc limit 5;
ALTER TABLE LeadTimesRate modify COLUMN LeadTimesRate decimal(38,6) NULL;
select * from LeadTimesRate order by collectedTime desc limit 5;
ALTER TABLE MaterialInventoryTurnoverDay modify COLUMN InventoryTurnoverRate decimal(38,6) NULL;
ALTER TABLE MaterialInventoryTurnoverDay modify COLUMN CostofGoods decimal(38,6) NULL;
ALTER TABLE MaterialInventoryTurnoverDay modify COLUMN BeginningInventory decimal(38,6) NULL;
ALTER TABLE MaterialInventoryTurnoverDay modify COLUMN EndingInventory decimal(38,6) NULL;
ALTER TABLE MaterialInventoryTurnoverDay modify COLUMN AverageInventory decimal(38,6) NULL;
select * from MaterialInventoryTurnoverDay order by collectedTime desc limit 5;
ALTER TABLE MaterialIssuanceCycle modify COLUMN Material_Issuance_Cycle decimal(38,6) NULL;
ALTER TABLE MaterialIssuanceCycle modify COLUMN Issuance_total_time decimal(38,6) NULL;
select * from MaterialIssuanceCycle order by collectedTime desc limit 5;
ALTER TABLE MissingrateOfWorkHour modify COLUMN Missingrate_WorkHour decimal(38,6) NULL;
select * from MissingrateOfWorkHour order by collectedTime desc limit 5;
ALTER TABLE MissingworkRate modify COLUMN Missing_work_rate decimal(38,6) NULL;
select * from MissingworkRate order by collectedTime desc limit 5;
ALTER TABLE ModuleMonthlyDays modify COLUMN SettlementCycle decimal(38,6) NULL;
ALTER TABLE ModuleMonthlyDays modify COLUMN SettlementDays decimal(38,6) NULL;
select * from ModuleMonthlyDays order by collectedTime desc limit 5;
ALTER TABLE NumberOfOrderChanges modify COLUMN order_change_num decimal(38,6) NULL;
ALTER TABLE NumberOfOrderChanges modify COLUMN Total_number_change_orders decimal(38,6) NULL;
ALTER TABLE NumberOfOrderChanges modify COLUMN Total_number_change decimal(38,6) NULL;
select * from NumberOfOrderChanges order by collectedTime desc limit 5;
ALTER TABLE OrderChangeCoverageRate modify COLUMN Change_frequency decimal(38,6) NULL;
ALTER TABLE OrderChangeCoverageRate modify COLUMN CoverageRate decimal(38,6) NULL;
select * from OrderChangeCoverageRate order by collectedTime desc limit 5;
ALTER TABLE OrderLeadTime modify COLUMN Order_lead_time decimal(38,6) NULL;
ALTER TABLE OrderLeadTime modify COLUMN Delivery_time decimal(38,6) NULL;
select * from OrderLeadTime order by collectedTime desc limit 5;
ALTER TABLE OrdertoWork modify COLUMN OrdertoWork decimal(38,6) NULL;
ALTER TABLE OrdertoWork modify COLUMN OrdertoWorkTime decimal(38,6) NULL;
select * from OrdertoWork order by collectedTime desc limit 5;
ALTER TABLE OverdueAccountsPayable modify COLUMN overdue_accounts_pay decimal(38,6) NULL;
select * from OverdueAccountsPayable order by collectedTime desc limit 5;
ALTER TABLE OverdueAccountsReceiveRate modify COLUMN overdue_receive_rate decimal(38,6) NULL;
select * from OverdueAccountsReceiveRate order by collectedTime desc limit 5;
ALTER TABLE PayableReconciliationRate modify COLUMN make_up_order_rate decimal(38,6) NULL;
ALTER TABLE PayableReconciliationRate modify COLUMN Replenishment_time decimal(38,6) NULL;
ALTER TABLE PayableReconciliationRate modify COLUMN Document_review_time decimal(38,6) NULL;
ALTER TABLE PaymentApprovalCycle modify COLUMN Payment_approval_cycle decimal(38,6) NULL;
ALTER TABLE PaymentApprovalCycle modify COLUMN Payment_approval_time decimal(38,6) NULL;
ALTER TABLE PaymentVerificationCycle modify COLUMN Payment_verification_cycle decimal(38,6) NULL;
ALTER TABLE PaymentVerificationCycle modify COLUMN payment_verification_time decimal(38,6) NULL;
ALTER TABLE ProcurementCycle modify COLUMN Procurement_cycle decimal(38,6) NULL;
ALTER TABLE ProcurementCycle modify COLUMN Total_procurement_time decimal(38,6) NULL;
ALTER TABLE ProcurementSourcesRate modify COLUMN CoverageRate decimal(38,6) NULL;
ALTER TABLE ProcurementTypeDeliveryRate modify COLUMN Procurement_delivery_rate decimal(38,6) NULL;
ALTER TABLE ProductInventoryTurnover modify COLUMN InventoryTurnoverRate decimal(38,6) NULL;
ALTER TABLE ProductInventoryTurnover modify COLUMN CostofGoods decimal(38,6) NULL;
ALTER TABLE ProductInventoryTurnover modify COLUMN BeginningInventory decimal(38,6) NULL;
ALTER TABLE ProductInventoryTurnover modify COLUMN EndingInventory decimal(38,6) NULL;
ALTER TABLE ProductInventoryTurnover modify COLUMN AverageInventory decimal(38,6) NULL;
ALTER TABLE ProductProductionCycle modify COLUMN Product_production_cycle decimal(38,6) NULL;
ALTER TABLE ProductProductionCycle modify COLUMN Total_production_time decimal(38,6) NULL;
ALTER TABLE ProductionCycle modify COLUMN production_cycle decimal(38,6) NULL;
ALTER TABLE ProductionCycle modify COLUMN Total_production_time decimal(38,6) NULL;
ALTER TABLE ProductionSourceRate modify COLUMN CoverageRate decimal(38,6) NULL;
ALTER TABLE ProductionYield modify COLUMN production_yield decimal(38,6) NULL;
ALTER TABLE ProductionYield modify COLUMN production_quantity decimal(38,6) NULL;
ALTER TABLE ProductionYield modify COLUMN quantity_good_products decimal(38,6) NULL;
ALTER TABLE PurchaseQualityDefectRate modify COLUMN Purchase_quality_defect_rate decimal(38,6) NULL;
ALTER TABLE PurchaseQualityDefectRate modify COLUMN NumberofDefects decimal(38,6) NULL;
ALTER TABLE PurchaseQualityDefectRate modify COLUMN Received_quantity decimal(38,6) NULL;
ALTER TABLE PurchaseZeroUnitPriceRatio modify COLUMN zero_unit_price_ratio decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceAndPurchase modify COLUMN rate_purchase_invoice decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceAndPurchase modify COLUMN diff_purchase_invoice decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceAndPurchase modify COLUMN Purchase_price decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceOrder modify COLUMN rate_invoice_order decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceOrder modify COLUMN diff_invoice_order decimal(38,6) NULL;
ALTER TABLE RateBetweenInvoiceOrder modify COLUMN order_price decimal(38,6) NULL;
ALTER TABLE RateOfOrderChange modify COLUMN Order_change_rate decimal(38,6) NULL;
ALTER TABLE RateOfPreparationLeadTime modify COLUMN RateOfMaterialPreparation decimal(38,6) NULL;
ALTER TABLE ReceivableTurnoverRate modify COLUMN Receivable_turnover_rate decimal(38,6) NULL;
ALTER TABLE SemiFinishProductProductionCycle modify COLUMN SemiFinishCycle decimal(38,6) NULL;
ALTER TABLE SemiFinishProductProductionCycle modify COLUMN Total_production_time decimal(38,6) NULL;
ALTER TABLE StartRateOfWork modify COLUMN Start_work_rate decimal(38,6) NULL;
ALTER TABLE StartWorkRate modify COLUMN Start_work_rate decimal(38,6) NULL;
ALTER TABLE TimelyCompletionOfWork modify COLUMN Timely_completion_work decimal(38,6) NULL;
ALTER TABLE WorkOrderReportRateBatch modify COLUMN reporting_rate decimal(38,6) NULL;
ALTER TABLE WorkOrderReportRateSingle modify COLUMN reporting_rate decimal(38,6) NULL;
