#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析SQL文件，提取FROM后面的表名
然后 使用 mysql -h10.100.126.132 -P19030 -uroot -pdigiwin@123 --database=servicecloud --skip-column-names --raw --silent
然后 使用source show.sql
然后 复制
"""

import re
import os
from typing import Set, List

def extract_table_names_from_file(file_path: str) -> Set[str]:
    """
    从SQL文件中提取所有FROM后面的表名
    
    Args:
        file_path: SQL文件路径
        
    Returns:
        包含所有表名的集合
    """
    table_names = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        # 使用正则表达式匹配FROM后面的表名
        # 匹配模式：from 后面跟着可选的空格，然后是表名
        # 表名可能包含字母、数字、下划线、点号（用于schema.table格式）
        pattern = r'\bfrom\s+([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)?)\b'
        
        # 查找所有匹配项（忽略大小写）
        matches = re.findall(pattern, content, re.IGNORECASE)
        
        # 将匹配到的表名添加到集合中
        for match in matches:
            table_names.add("SHOW CREATE TABLE "+match.strip()+" ;")
            
    except FileNotFoundError:
        print(f"错误：文件 {file_path} 不存在")
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        
    return table_names

def save_table_names_to_file(table_names: Set[str], output_file: str = "table_names.txt"):
    """
    将表名保存到文件中
    
    Args:
        table_names: 表名集合
        output_file: 输出文件名
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as file:
            for table_name in sorted(table_names):
                file.write(f"{table_name}\n")
        print(f"表名已保存到文件：{output_file}")
    except Exception as e:
        print(f"保存文件时发生错误：{e}")

def main():
    """主函数"""
    # 文件路径
    sql_file_path = r"D:\work\demo\src\main\resources\tbb\hive_to_impala"
    
    # 检查文件是否存在
    if not os.path.exists(sql_file_path):
        print(f"错误：文件 {sql_file_path} 不存在")
        return
    
    print(f"正在解析文件：{sql_file_path}")
    
    # 提取表名
    table_names = extract_table_names_from_file(sql_file_path)
    
    if table_names:
        print(f"\n找到 {len(table_names)} 个表名：")
        for i, table_name in enumerate(sorted(table_names), 1):
            print(f"{i:2d}. {table_name}")
        
        # 保存到文件
        # save_table_names_to_file(table_names)
        
        # 生成用于StarRocks查询的表名列表
        generate_starrocks_query_list(table_names)
        
    else:
        print("未找到任何表名")

def generate_starrocks_query_list(table_names: Set[str], output_file: str = "starrocks_tables.txt"):
    """
    生成用于StarRocks查询的表名列表（去掉schema前缀）
    
    Args:
        table_names: 表名集合
        output_file: 输出文件名
    """
    try:
        starrocks_tables = set()
        
        for table_name in table_names:
            # 如果表名包含schema（如tbb.table_name），只取表名部分
            if '.' in table_name:
                table_only = table_name.split('.')[-1]
                starrocks_tables.add(table_only)
            else:
                starrocks_tables.add(table_name)
        
        with open(output_file, 'w', encoding='utf-8') as file:
            for table_name in sorted(starrocks_tables):
                file.write(f"{table_name}\n")
        
        print(f"\nStarRocks查询用表名已保存到文件：{output_file}")
        print(f"共 {len(starrocks_tables)} 个唯一表名")
        
    except Exception as e:
        print(f"生成StarRocks表名列表时发生错误：{e}")

if __name__ == "__main__":
    main()
